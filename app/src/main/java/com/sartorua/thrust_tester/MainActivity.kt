package com.sartorua.thrust_tester

import android.os.Bundle
import android.util.Log
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.updatePadding
import com.google.android.material.bottomnavigation.BottomNavigationView
import androidx.appcompat.app.AppCompatActivity
import androidx.navigation.findNavController
import androidx.navigation.ui.setupWithNavController
import com.sartorua.thrust_tester.databinding.ActivityMainBinding

class MainActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "MainActivity"
    }

    private lateinit var binding: ActivityMainBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        try {
            Log.d(TAG, "Starting MainActivity onCreate")

            binding = ActivityMainBinding.inflate(layoutInflater)
            setContentView(binding.root)

            // Handle window insets for proper status bar and navigation bar handling
            ViewCompat.setOnApplyWindowInsetsListener(binding.root) { view, windowInsets ->
                val insets = windowInsets.getInsets(WindowInsetsCompat.Type.systemBars())

                // Apply top inset to the container to avoid status bar overlap
                view.updatePadding(top = insets.top)

                // Apply bottom inset to the navigation view to avoid navigation bar overlap
                binding.navView.updatePadding(bottom = insets.bottom)

                windowInsets
            }

            val navView: BottomNavigationView = binding.navView

            val navController = findNavController(R.id.nav_host_fragment_activity_main)
            navView.setupWithNavController(navController)

            Log.d(TAG, "MainActivity onCreate completed successfully")

        } catch (e: Exception) {
            Log.e(TAG, "Error in MainActivity onCreate", e)
            // Don't crash the app, but log the error
        }
    }
}